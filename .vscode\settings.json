{"Codegeex.Privacy": true, "bitoAI.codeCompletion.enableAutoCompletion": false, "bitoAI.codeCompletion.enableCommentToCode": true, "workbench.iconTheme": "material-icon-theme", "workbench.colorTheme": "Visual Studio 2019 Dark", "editor.formatOnSave": true, "C_Cpp.autocompleteAddParentheses": true, "C_Cpp.errorSquiggles": "enabled", "indentRainbow.colorOnWhiteSpaceOnly": true, "editor.wordWrap": "on", "editor.detectIndentation": false, "dotnet.codeLens.enableReferencesCodeLens": false, "editor.guides.bracketPairs": "active", "editor.mouseWheelZoom": true, "editor.minimap.maxColumn": 32, "editor.minimap.size": "fill", "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "codeium.enableCodeLens": "hide_x", "codeium.enableConfig": {"*": true}, "editor.fontSize": 18, "smartInput.inputMethodType": "搜狗拼音", "bitoAI.appearance.fontSize (Match with IDE Font)": false, "editor.cursorBlinking": "solid", "workbench.colorCustomizations": {"editorCursor.foreground": "#FF0000"}, "editor.inlineSuggest.showToolbar": "onHover", "chatgpt.lang": "cn", "chatgpt.model": "gpt-3.5-turbo-16k", "chatgpt.temperature": 0.5, "tabnine.experimentalAutoImports": true, "partialDiff.preComparisonTextNormalizationRules": [], "partialDiff.commandsOnContextMenu": {"markSection1": false, "markSection2AndTakeDiff": false, "diffVisibleEditors": false, "togglePreComparisonTextNormalizationRules": false}, "fittencode.languagePreference.answerPreference": "zh-cn", "fittencode.languagePreference.commentPreference": "zh-cn", "explorer.confirmDelete": false, "smartInput.languageChangeHoverTip": false, "explorer.confirmDragAndDrop": false, "workbench.editorAssociations": {"*.bundle": "default"}, "fittencode.languagePreference.displayPreference": "zh-cn", "Codegeex.License": "", "augment.chat.userGuidelines": "Always respond in Chinese-simplified\nShow your thinking process in Chinese-simplified\n- 收到用户请求时不能直接给出答案，必须先对用户提出的问题进行[需求分析]然后[任务规划]。将任务细化成多个子任务并建立一个任务清单输出给用户，等待用户给出明确的指令后，才可以逐一执行清单列表中的任务，直至清单所有任务全部执行完毕。\n- 如非必要，不要使用Emoji，也不要中文英文混排。", "workbench.secondarySideBar.showLabels": false, "codingcopilot.floatShortcut": false, "fittencode.selection.showCodeLens": false, "editor.minimap.enabled": true, "cursor.chat.view.titleMode": "full", "markdown.validate.enabled": false, "diffEditor.renderSideBySide": true}