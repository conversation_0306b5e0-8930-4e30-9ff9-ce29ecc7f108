
# 整体构思
（这里是作者设计思路的文稿，不要删改覆盖整体构思章节的内容）
- **故事时间点**：
- **故事舞台构建**：
- **核心目的**：
- **设计思路**：
	- （公式：主要势力或下属势力+利益矛盾+计谋标签）
	- （现在在哪+哪些人可能会出现在这里+他们在这里的原因是什么）
	- （是否需要引入特殊职业角色）
- **禁止内容**：
	- 

### 故事弧设计
- 叙事模型选择：
	- 钩子、升级、转折（功能性模型）
	- 开始、发展、高潮、结局（四段式故事弧）
	- 开始、发展、意外、结局（起承转合）
	- 阻碍、（通用）

第一阶段：钩子
- 核心任务：
- 关键问题：
	- 亮出主角
	- 给出动机
	- 引爆冲突
	- 埋下悬念
	- 结尾高能

第二阶段：升级
- 核心任务：
- 关键问题：
	- 解开近忧
	- 引出远患
	- 价值展现
	- 引入变量

第三阶段：转折
- 核心任务：
- 关键问题：
	- 从“逃”到“进”
	- 确立短期目标
	- 离开新手村
	- 展现决心

第三阶段：（XX）
- 核心任务：
- 关键问题：
	- 
	- 
	- 

# 设计规划

**时间节点**：
**设计目的**：
**核心目标**：

**首次登场**：（*如果人物库中没有合适本章内容的角色，需要新增角色，你可以直接使用“新增角色A”来表示该人物，并写出该角色的必要属性，我会及时新建设计对应角色来替换这个“新增角色”*）
**出场人物**：
**主要舞台**：

**关键行动**：
**受到阻碍**：
**矛盾冲突**：

**主要事件与转折点**：
**重要对白**：

**不在本章内容但对白提及**：
**主要情节线索与伏笔安排**：（当前剧情发生的线索与对应的伏笔）
**是否计划长线伏笔与回收**：
**是否设计悬念和情节钩子**：
**是否安排剧情反转**：

## 前置条件伏笔与铺垫：
（这里写的是完成本次剧情需要的前置条件，哪些剧情需要前期做铺垫或埋下伏笔）

## 关键信息与设定缺失补全
（当剧情设计时缺少重要的关键信息时，在这里设计补充信息设定，让故事逻辑自洽）

# 故事弧
整体剧情简介：

## 第一幕：

#### 事件一：

- **场景搭建**：
- **故事情节**：
- **设计目的**：
	1. 
	2. 

#### 事件二：

- **场景搭建**：
- **故事情节**：
- **设计目的**：
	1. 
	2. 

# 后续故事的剧情钩子
（本节内容用于记录当前剧情设计为未来故事留下的可能性，作为创作的灵感参考。）